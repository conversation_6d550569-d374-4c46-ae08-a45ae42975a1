import React from 'react';

interface ChatHeaderProps {
  onClose: () => void;
  onClearSession: () => void;
  chatIconUrl?: string;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({ onClose, onClearSession, chatIconUrl = "/quoteai.png" }) => {
  return (
    <div className="chat-header">
      <img src={chatIconUrl} alt="Chatbot Icon" className="chat-icon" />
      Chat with us!
      <div className="header-buttons">
        <button
          className="clear-button"
          onClick={onClearSession}
          title="Clear chat history and start new session"
          aria-label="Clear chat history"
        >
          🗑️
        </button>
        <button
          className="close-button"
          onClick={onClose}
          title="Close chat"
          aria-label="Close chat"
        >
          ×
        </button>
      </div>
    </div>
  );
};

export default ChatHeader;