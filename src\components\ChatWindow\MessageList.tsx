import React, { useEffect, useRef } from 'react';
import type { Message, Suggestion } from '../../types';
import MessageItem from './MessageItem';

interface MessageListProps {
  messages: Message[];
  isLoading: boolean; // For bot typing indicator
  queueLength: number;
  onSuggestionClick: (suggestion: Suggestion) => void;
  onImageClick: (url: string) => void;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  isLoading,
  queueLength,
  onSuggestionClick,
  onImageClick,
}) => {
  const chatEndRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isLoading]); // Scroll on new messages or when loading indicator appears/disappears

  return (
    <div className="chat-messages" aria-live="polite" aria-atomic="false">
      {messages.map((msg, index) => (
        <MessageItem
          key={`${msg.type}-${msg.timestamp}-${index}`} // More robust key
          message={msg}
          onSuggestionClick={onSuggestionClick}
          onImageClick={onImageClick}
        />
      ))}
      {isLoading && (
        <div className="loading-dots" aria-label="Bot is typing">
          <span>.</span>
          <span>.</span>
          <span>.</span>
        </div>
      )}
      {/* Queue status indicator - consider if this is part of messages or separate UI */}
      {queueLength > 0 && !isLoading && ( // Show only if not already showing main loading
          <div className="queue-indicator">
              {queueLength} message{queueLength > 1 ? 's' : ''} in queue
          </div>
      )}
      <div ref={chatEndRef} />
    </div>
  );
};

export default MessageList;