/**
 * Application-wide constants
 */

export const DEFAULT_API_URL = 'https://localhost:8080'; // Replace with production URL
export const DEFAULT_API_KEY = ';C\\}/{ap,76L\'*m7U2!{K|21?n[*4i`;QuL'; // Placeholder, should be configured securely

export const SESSION_EXPIRY_MS = 3 * 24 * 60 * 60 * 1000; // 3 days
export const MAX_IMAGES_UPLOAD = 8;
export const MAX_IMAGE_SIZE_BYTES = 8 * 1024 * 1024; // 8MB

export const WELCOME_MESSAGE_CONTENT = "G'day Mate, how can I help?";
export const DEFAULT_SUGGESTIONS = [
  { text: "Get a quote", value: "I'd like to get a quote for your services." },
  { text: "Service areas", value: "What areas do you service?" },
  { text: "Pricing", value: "Can you tell me about your pricing?" },
  { text: "Availability", value: "When are you available?" }
];

export const CHAT_STORAGE_PREFIX = 'quoteai_chat_';
export const CURRENT_SESSION_ID_KEY = 'quoteai_current_session_id';

export const ERROR_MESSAGES = {
  INIT_FAILED: 'Failed to initialize chatbot. Please try refreshing the page.',
  SEND_FAILED: 'Sorry, I couldn\'t send your message. Please try again.',
  UPLOAD_FAILED: 'Failed to upload image. Please try again.',
  FILE_TYPE_INVALID: 'Please select only image files (PNG, JPG, GIF).',
  FILE_TOO_LARGE: `File size exceeds the ${MAX_IMAGE_SIZE_BYTES / (1024 * 1024)}MB limit.`,
  SERVER_BUSY: 'The server is currently busy, please try again in a moment.',
  THREAD_ACTIVE_RUN: 'The conversation is currently processing a previous request. Please wait a moment.',
  GENERIC_ERROR: 'An unexpected error occurred. Please try again.'
};

// API Endpoints (relative to API_BASE_URL)
export const API_ENDPOINTS = {
  INITIATE_CONVERSATION: '/initiate_conversation',
  CHAT: '/chat',
  UPLOAD: '/upload',
};