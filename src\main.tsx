import React from 'react';
import { createRoot, type Root } from 'react-dom/client';
import ChatWidget from './ChatWidget.tsx';
import { DEFAULT_API_KEY, DEFAULT_API_URL } from './constants.ts'; // Use constants

export interface WidgetConfig {
  apiKey?: string;
  apiUrl?: string;
  websiteUrl?: string;
  clientId?: string;
  uuid?: string;
  customer_name?: string;
}

declare global {
  interface Window {
    initChatWidget?: (config?: WidgetConfig) => void;
    QUOTE_AI_API_URL?: string; // Global API URL override
    QUOTE_AI_CONFIG?: WidgetConfig; // Store widget configuration globally
    debugQuoteAI?: { // Optional: keep for debugging if desired
      getThreadId: () => Record<string, any>;
    };
  }
}

// Optional: keep global debug function if needed
if (import.meta.env.DEV && !window.debugQuoteAI) { // Only in development
    window.debugQuoteAI = {
      getThreadId: function() {
        const items: Record<string, any> = {};
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith('quoteai_')) {
            try {
              items[key] = JSON.parse(localStorage.getItem(key)!);
            } catch (e) {
              items[key] = localStorage.getItem(key);
            }
          }
        }
        console.log('QuoteAI storage items:', items);
        return items;
      }
    };
}


// Keep track of the React root to prevent multiple createRoot calls
let widgetRoot: Root | null = null;

const renderWidget = (config: WidgetConfig) => {
  let container = document.getElementById('quoteai-widget-container');
  if (!container) {
    container = document.createElement('div');
    container.id = 'quoteai-widget-container';
    document.body.appendChild(container);
  }

  // Store config globally for API access
  window.QUOTE_AI_CONFIG = config;

  // Create root only once, reuse for subsequent renders
  if (!widgetRoot) {
    widgetRoot = createRoot(container!);
  }

  widgetRoot.render(
    <React.StrictMode>
      <ChatWidget apiKey={config.apiKey || DEFAULT_API_KEY} apiUrl={config.apiUrl || DEFAULT_API_URL} />
    </React.StrictMode>
  );
};

// Auto-initialize the widget
const autoInitializeWidget = () => {
  console.info('QuoteAI Widget: Auto-initializing...');
  const config: WidgetConfig = {
    apiUrl: window.QUOTE_AI_API_URL || DEFAULT_API_URL,
    apiKey: DEFAULT_API_KEY,
  };

  renderWidget(config);
};

// Provide manual initialization function
window.initChatWidget = function(config = {}) {
  console.info('QuoteAI Widget: Manual initialization with config:', config);

  const fullConfig: WidgetConfig = {
    apiKey: config.apiKey || DEFAULT_API_KEY,
    apiUrl: config.apiUrl || window.QUOTE_AI_API_URL || DEFAULT_API_URL,
    websiteUrl: config.websiteUrl,
    clientId: config.clientId,
    uuid: config.uuid,
    customer_name: config.customer_name,
  };

  if (config.apiUrl) {
    window.QUOTE_AI_API_URL = config.apiUrl; // Allow global override via init
  }

  renderWidget(fullConfig);
};

// Auto-initialize if not manually done by the time DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Check if initChatWidget was called or if auto-init should proceed
  // A simple check: if the container isn't there, auto-init hasn't run.
  // If initChatWidget is expected to be called by users, they might call it before DOMContentLoaded.
  // To prevent double initialization, ensure renderWidget is idempotent or checks.
  if (!document.getElementById('quoteai-widget-container')) {
     // Check if widget has already been initialized (e.g. by a manual call to initChatWidget)
     // This simple check is okay because renderWidget will reuse the container if it exists.
    autoInitializeWidget();
  }
});

// Export ChatWidget if this module is imported elsewhere (e.g. for library builds)
// For a simple script embed, this export isn't strictly necessary for window.initChatWidget to work.
export default ChatWidget;