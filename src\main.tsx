import React from 'react';
import { createRoot } from 'react-dom/client';
import ChatWidget from './ChatWidget.tsx';
import { DEFAULT_API_KEY, DEFAULT_API_URL } from './constants.ts'; // Use constants

declare global {
  interface Window {
    initChatWidget?: (config?: {
      apiKey?: string;
      apiUrl?: string;
    }) => void;
    QUOTE_AI_API_URL?: string; // Global API URL override
    debugQuoteAI?: { // Optional: keep for debugging if desired
      getThreadId: () => Record<string, any>;
    };
  }
}

// Optional: keep global debug function if needed
if (import.meta.env.DEV && !window.debugQuoteAI) { // Only in development
    window.debugQuoteAI = {
      getThreadId: function() {
        const items: Record<string, any> = {};
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith('quoteai_')) {
            try {
              items[key] = JSON.parse(localStorage.getItem(key)!);
            } catch (e) {
              items[key] = localStorage.getItem(key);
            }
          }
        }
        console.log('QuoteAI storage items:', items);
        return items;
      }
    };
}


const renderWidget = (props: { apiKey: string; apiUrl: string }) => {
  let container = document.getElementById('quoteai-widget-container');
  if (!container) {
    container = document.createElement('div');
    container.id = 'quoteai-widget-container';
    document.body.appendChild(container);
  }

  // Ensure only one instance is rendered
  const root = createRoot(container!); // container is guaranteed to exist
  root.render(
    <React.StrictMode>
      <ChatWidget apiKey={props.apiKey} apiUrl={props.apiUrl} />
    </React.StrictMode>
  );
};

// Auto-initialize the widget
const autoInitializeWidget = () => {
  console.info('QuoteAI Widget: Auto-initializing...');
  const apiUrl = window.QUOTE_AI_API_URL || DEFAULT_API_URL;
  const apiKey = DEFAULT_API_KEY; // Default API key if not configured by initChatWidget

  renderWidget({ apiKey, apiUrl });
};

// Provide manual initialization function
window.initChatWidget = function(config = {}) {
  console.info('QuoteAI Widget: Manual initialization with config:', config);

  const apiKey = config.apiKey || DEFAULT_API_KEY;
  const apiUrlFromConfig = config.apiUrl || window.QUOTE_AI_API_URL || DEFAULT_API_URL;

  if (config.apiUrl) {
    window.QUOTE_AI_API_URL = config.apiUrl; // Allow global override via init
  }

  renderWidget({ apiKey, apiUrl: apiUrlFromConfig });
};

// Auto-initialize if not manually done by the time DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Check if initChatWidget was called or if auto-init should proceed
  // A simple check: if the container isn't there, auto-init hasn't run.
  // If initChatWidget is expected to be called by users, they might call it before DOMContentLoaded.
  // To prevent double initialization, ensure renderWidget is idempotent or checks.
  if (!document.getElementById('quoteai-widget-container')) {
     // Check if widget has already been initialized (e.g. by a manual call to initChatWidget)
     // This simple check is okay because renderWidget will reuse the container if it exists.
    autoInitializeWidget();
  }
});

// Export ChatWidget if this module is imported elsewhere (e.g. for library builds)
// For a simple script embed, this export isn't strictly necessary for window.initChatWidget to work.
export default ChatWidget;