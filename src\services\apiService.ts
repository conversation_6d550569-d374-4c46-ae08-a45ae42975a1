/**
 * API service for interacting with the backend
 */
import {
  DEFAULT_API_URL,
  API_ENDPOINTS,
  ERROR_MESSAGES,
} from '../constants';
import type { WidgetConfig } from '../main';

// Extend global window interface
declare global {
  interface Window {
    QUOTE_AI_CONFIG?: WidgetConfig;
    QUOTE_AI_API_URL?: string;
  }
}

interface InitiateConversationResponse {
  success: boolean;
  thread_id: string;
}

interface ChatResponse {
  response: string;
  suggestions?: { text: string; value: string }[];
}

interface UploadResponse {
  success: boolean;
  file_url: string;
  filename: string;
}

const getApiBaseUrl = (apiUrl?: string): string => {
  return apiUrl || window.QUOTE_AI_API_URL || DEFAULT_API_URL;
};

async function handleApiResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const errorText = await response.text().catch(() => 'Failed to read error response.');
    let errorMessage = ERROR_MESSAGES.GENERIC_ERROR;
    if (response.status === 401 || response.status === 403) {
        errorMessage = "Authentication failed. Please check your API key.";
    } else if (response.status === 429) {
        errorMessage = ERROR_MESSAGES.SERVER_BUSY;
    } else if (response.status === 409) {
        errorMessage = ERROR_MESSAGES.THREAD_ACTIVE_RUN;
    } else if (response.status >= 500) {
        errorMessage = "A server error occurred. Please try again later.";
    } else {
        errorMessage = `Error ${response.status}: ${errorText || 'Request failed'}`;
    }
    const error: any = new Error(errorMessage);
    error.status = response.status; // Attach status for more specific handling
    throw error;
  }
  return response.json() as Promise<T>;
}

export const initiateConversationApi = async (
  apiKey: string,
  customApiUrl?: string
): Promise<InitiateConversationResponse> => {
  const API_BASE_URL = getApiBaseUrl(customApiUrl);

  // Get configuration data from global window object
  const config = window.QUOTE_AI_CONFIG || {};

  // Prepare request body with available configuration data
  const requestBody: any = {};

  // Add fields that the backend expects
  if (config.customer_name) {
    requestBody.customer_name = config.customer_name;
  }

  if (config.uuid) {
    requestBody.uuid = config.uuid;
  }

  if (config.websiteUrl) {
    requestBody.website_url = config.websiteUrl;
  }

  // If no specific data is provided, send a default customer_name
  if (Object.keys(requestBody).length === 0) {
    requestBody.customer_name = 'Anonymous User';
  }

  const headers: any = {
    'Content-Type': 'application/json',
    'x-api-key': apiKey,
  };

  // Add Client-ID header if available
  if (config.clientId) {
    headers['Client-ID'] = config.clientId;
  }

  const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.INITIATE_CONVERSATION}`, {
    method: 'POST',
    headers,
    body: JSON.stringify(requestBody),
  });
  return handleApiResponse<InitiateConversationResponse>(response);
};

export const sendMessageApi = async (
  apiKey: string,
  threadId: string,
  message: string,
  attachments: string[],
  customApiUrl?: string
): Promise<ChatResponse> => {
  const API_BASE_URL = getApiBaseUrl(customApiUrl);
  const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.CHAT}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': apiKey,
    },
    body: JSON.stringify({
      thread_id: threadId,
      message: message,
      attachments: attachments,
    }),
  });
  return handleApiResponse<ChatResponse>(response);
};

export const uploadImageApi = async (
  apiKey: string,
  threadId: string,
  file: File,
  customApiUrl?: string
): Promise<UploadResponse> => {
  const API_BASE_URL = getApiBaseUrl(customApiUrl);
  const formData = new FormData();
  formData.append('file', file);
  formData.append('thread_id', threadId);

  const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.UPLOAD}`, {
    method: 'POST',
    headers: {
      'x-api-key': apiKey,
      // 'Content-Type': 'multipart/form-data' is set automatically by browser for FormData
    },
    body: formData,
  });
  return handleApiResponse<UploadResponse>(response);
};